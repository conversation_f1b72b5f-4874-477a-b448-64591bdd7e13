{"format": 1, "restore": {"D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.Example.csproj": {}}, "projects": {"D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.csproj", "projectName": "Pico.Spatializer", "projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Temp\\obj\\Debug\\Pico.Spatializer\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.csproj": {"projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.Example.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.Example.csproj", "projectName": "Pico.Spatializer.Example", "projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.Example.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Temp\\obj\\Debug\\Pico.Spatializer.Example\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.csproj": {"projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Pico.Spatializer.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.csproj", "projectName": "Unity.XR.PICO", "projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Temp\\obj\\Debug\\Unity.XR.PICO\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.LivePreview.csproj": {"projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.LivePreview.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}, "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.LivePreview.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.LivePreview.csproj", "projectName": "Unity.XR.PICO.LivePreview", "projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.PICO.LivePreview.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Temp\\obj\\Debug\\Unity.XR.PICO.LivePreview\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}