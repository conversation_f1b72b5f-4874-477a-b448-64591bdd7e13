{"format": 1, "restore": {"D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {}}, "projects": {"D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "projectName": "Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor", "projectPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\xjj\\UnityProject\\VRAssembly\\VRAssembly\\Temp\\obj\\Debug\\Unity.XR.Interaction.Toolkit.Samples.StarterAssets.Editor\\", "projectStyle": "PackageReference", "fallbackFolders": ["D:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201\\RuntimeIdentifierGraph.json"}}}}}